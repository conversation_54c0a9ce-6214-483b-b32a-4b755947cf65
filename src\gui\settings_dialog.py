"""
Settings dialog for the file transfer application.
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import json
from pathlib import Path
from typing import Dict, Any, Optional
from src.utils.network_utils import NetworkUtils
from src.utils.logger import get_logger


class SettingsDialog:
    """
    Dialog for configuring application settings.
    """
    
    def __init__(self, parent: tk.Tk, current_settings: Optional[Dict[str, Any]] = None):
        """
        Initialize the settings dialog.

        Args:
            parent: Parent window
            current_settings: Current application settings
        """
        self.parent = parent
        self.settings = current_settings or self._get_default_settings()
        self.result = None
        self.logger = get_logger()
        
        # Create dialog
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("File Transfer Settings")
        self.dialog.geometry("700x600")
        self.dialog.resizable(True, True)
        
        # Make dialog modal
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # Center dialog
        self._center_dialog()
        
        # Create widgets
        self._create_widgets()
        
        # Handle dialog close
        self.dialog.protocol("WM_DELETE_WINDOW", self._on_cancel)
    
    def _get_default_settings(self) -> Dict[str, Any]:
        """Get default application settings."""
        return {
            # Network settings
            "chunk_size_kb": 8,
            "connection_timeout": 10,
            "max_connections": 5,
            "default_server_port": 8888,
            "default_server_host": "0.0.0.0",
            "auto_start_server": False,

            # Speed optimization settings
            "enable_speed_optimization": True,
            "max_speed_mbps": 0,  # 0 = unlimited
            "speed_mode": "balanced",  # conservative, balanced, aggressive, custom
            "enable_congestion_control": True,
            "enable_speed_ramping": True,
            "auto_detect_bandwidth": True,
            "chunk_size_min_kb": 8,
            "chunk_size_max_kb": 1024,
            "buffer_size_min_kb": 64,
            "buffer_size_max_kb": 4096,

            # Security settings
            "enable_encryption": False,
            "verify_checksums": True,
            "require_authentication": False,

            # Compression settings
            "enable_compression": True,
            "compression_method": "auto",
            "compression_level": 6,
            "min_compression_size_kb": 1,
            "skip_compressed_files": True,

            # File transfer settings
            "default_download_dir": str(Path.home() / "Downloads" / "FileTransfer"),
            "overwrite_existing_files": False,
            "create_subdirectories": True,
            "preserve_file_timestamps": True,

            # UI settings
            "show_transfer_notifications": True,
            "auto_clear_completed_transfers": False,
            "confirm_file_deletions": True,
            "remember_window_size": True,
            "show_speed_graph": True,
            "show_network_utilization": True,

            # Advanced settings
            "enable_logging": True,
            "log_level": "INFO",
            "max_log_file_size_mb": 10,
            "enable_statistics": True,
            "auto_update_check": True
        }
    
    def _create_widgets(self):
        """Create and layout dialog widgets."""
        # Apply modern theme
        from src.gui.theme import ModernTheme
        ModernTheme.apply_theme(self.dialog)

        main_frame = ttk.Frame(self.dialog, padding="15")
        main_frame.pack(fill=tk.BOTH, expand=True)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(1, weight=1)

        # Title
        title_label = ttk.Label(main_frame, text="🔧 File Transfer Settings", style='Heading.TLabel')
        title_label.grid(row=0, column=0, pady=(0, 15), sticky=(tk.W, tk.E))

        # Create notebook for settings categories with full-width layout
        notebook = ttk.Notebook(main_frame)
        notebook.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 15))

        # Create comprehensive settings tabs
        self._create_compression_tab(notebook)
        self._create_security_tab(notebook)
        self._create_network_tab(notebook)
        self._create_file_transfer_tab(notebook)
        self._create_ui_tab(notebook)
        self._create_advanced_tab(notebook)

        # Buttons frame with full-width layout
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=2, column=0, sticky=(tk.W, tk.E))

        # Buttons with proper styling
        ttk.Button(button_frame, text="Reset to Defaults",
                  command=self._reset_to_defaults, style='Secondary.TButton').pack(side=tk.LEFT)

        ttk.Button(button_frame, text="Cancel",
                  command=self._on_cancel, style='Secondary.TButton').pack(side=tk.RIGHT, padx=(5, 0))

        ttk.Button(button_frame, text="Apply",
                  command=self._on_apply, style='Secondary.TButton').pack(side=tk.RIGHT, padx=(5, 0))

        ttk.Button(button_frame, text="OK",
                  command=self._on_ok, style='Primary.TButton').pack(side=tk.RIGHT, padx=(5, 0))

    def _create_compression_tab(self, notebook: ttk.Notebook):
        """Create the compression settings tab."""
        frame = ttk.Frame(notebook, padding="20")
        notebook.add(frame, text="🗜️ Compression")

        # Main compression settings
        main_group = ttk.LabelFrame(frame, text="Compression Settings", padding="15")
        main_group.pack(fill=tk.X, pady=(0, 15))

        # Enable compression
        self.compression_var = tk.BooleanVar(value=self.settings.get("enable_compression", True))
        compression_check = ttk.Checkbutton(
            main_group,
            text="Enable file compression",
            variable=self.compression_var,
            command=self._on_compression_toggle
        )
        compression_check.grid(row=0, column=0, sticky=tk.W, pady=(0, 10))

        # Compression info
        compression_info = ttk.Label(
            main_group,
            text="Compress files before transfer to reduce size and improve speed.\nBest for text files, documents, and code.",
            justify=tk.LEFT,
            style='Muted.TLabel'
        )
        compression_info.grid(row=1, column=0, sticky=tk.W, pady=(0, 15))

        # Compression method
        method_frame = ttk.Frame(main_group)
        method_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        ttk.Label(method_frame, text="Compression Method:").pack(side=tk.LEFT)
        self.compression_method_var = tk.StringVar(value=self.settings.get("compression_method", "auto"))
        method_combo = ttk.Combobox(
            method_frame,
            textvariable=self.compression_method_var,
            values=["auto", "gzip", "bzip2", "lzma", "zip"],
            state="readonly",
            width=15
        )
        method_combo.pack(side=tk.LEFT, padx=(10, 0))

        # Compression level
        level_frame = ttk.Frame(main_group)
        level_frame.grid(row=3, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        ttk.Label(level_frame, text="Compression Level:").pack(side=tk.LEFT)
        self.compression_level_var = tk.IntVar(value=self.settings.get("compression_level", 6))
        level_scale = ttk.Scale(
            level_frame,
            from_=1,
            to=9,
            variable=self.compression_level_var,
            orient=tk.HORIZONTAL,
            length=200
        )
        level_scale.pack(side=tk.LEFT, padx=(10, 5))

        self.level_label = ttk.Label(level_frame, text="6")
        self.level_label.pack(side=tk.LEFT, padx=(5, 0))
        level_scale.configure(command=self._update_compression_level)

        # Advanced compression settings
        advanced_group = ttk.LabelFrame(frame, text="Advanced Compression", padding="15")
        advanced_group.pack(fill=tk.X, pady=(0, 15))

        # Minimum file size for compression
        size_frame = ttk.Frame(advanced_group)
        size_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        ttk.Label(size_frame, text="Minimum file size for compression (KB):").pack(side=tk.LEFT)
        self.min_compression_size_var = tk.IntVar(value=self.settings.get("min_compression_size_kb", 1))
        size_spin = ttk.Spinbox(
            size_frame,
            from_=0,
            to=1024,
            textvariable=self.min_compression_size_var,
            width=10
        )
        size_spin.pack(side=tk.LEFT, padx=(10, 0))

        # Skip already compressed files
        self.skip_compressed_var = tk.BooleanVar(value=self.settings.get("skip_compressed_files", True))
        skip_check = ttk.Checkbutton(
            advanced_group,
            text="Skip already compressed files (jpg, png, zip, etc.)",
            variable=self.skip_compressed_var
        )
        skip_check.grid(row=1, column=0, sticky=tk.W, pady=(0, 5))

        # Compression statistics
        stats_group = ttk.LabelFrame(frame, text="Compression Statistics", padding="15")
        stats_group.pack(fill=tk.X)

        stats_info = ttk.Label(
            stats_group,
            text="• GZIP: Fast compression, good for most files\n• BZIP2: Better compression, slower speed\n• LZMA: Best compression, slowest speed\n• ZIP: Compatible, moderate compression\n• AUTO: Automatically selects best method",
            justify=tk.LEFT,
            style='Muted.TLabel'
        )
        stats_info.pack(anchor=tk.W)

        # Configure grid weights
        main_group.columnconfigure(0, weight=1)
        advanced_group.columnconfigure(0, weight=1)

    def _on_compression_toggle(self):
        """Handle compression enable/disable toggle."""
        # Could add logic to enable/disable related controls
        pass

    def _update_compression_level(self, value):
        """Update compression level label."""
        level = int(float(value))
        self.level_label.configure(text=str(level))

        # Update tooltip based on level
        if level <= 3:
            tooltip = "Fast compression"
        elif level <= 6:
            tooltip = "Balanced"
        else:
            tooltip = "Best compression"

        # You could add a tooltip here if desired

    def _create_file_transfer_tab(self, notebook: ttk.Notebook):
        """Create the file transfer settings tab."""
        frame = ttk.Frame(notebook, padding="20")
        notebook.add(frame, text="📁 File Transfer")

        # File handling settings
        file_group = ttk.LabelFrame(frame, text="File Handling", padding="15")
        file_group.pack(fill=tk.X, pady=(0, 15))

        # Overwrite existing files
        self.overwrite_var = tk.BooleanVar(value=self.settings.get("overwrite_existing_files", False))
        overwrite_check = ttk.Checkbutton(
            file_group,
            text="Overwrite existing files without asking",
            variable=self.overwrite_var
        )
        overwrite_check.grid(row=0, column=0, sticky=tk.W, pady=(0, 5))

        # Create subdirectories
        self.subdirs_var = tk.BooleanVar(value=self.settings.get("create_subdirectories", True))
        subdirs_check = ttk.Checkbutton(
            file_group,
            text="Create subdirectories as needed",
            variable=self.subdirs_var
        )
        subdirs_check.grid(row=1, column=0, sticky=tk.W, pady=(0, 5))

        # Preserve timestamps
        self.timestamps_var = tk.BooleanVar(value=self.settings.get("preserve_file_timestamps", True))
        timestamps_check = ttk.Checkbutton(
            file_group,
            text="Preserve original file timestamps",
            variable=self.timestamps_var
        )
        timestamps_check.grid(row=2, column=0, sticky=tk.W, pady=(0, 10))

        # Default download directory
        dir_frame = ttk.Frame(file_group)
        dir_frame.grid(row=3, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        ttk.Label(dir_frame, text="Default download directory:").pack(anchor=tk.W, pady=(0, 5))

        dir_entry_frame = ttk.Frame(dir_frame)
        dir_entry_frame.pack(fill=tk.X)

        self.download_dir_var = tk.StringVar(value=self.settings.get("default_download_dir", ""))
        dir_entry = ttk.Entry(dir_entry_frame, textvariable=self.download_dir_var)
        dir_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))

        browse_btn = ttk.Button(dir_entry_frame, text="Browse...",
                               command=self._browse_download_dir, style='Small.TButton')
        browse_btn.pack(side=tk.RIGHT)

        # Configure grid weights
        file_group.columnconfigure(0, weight=1)
        dir_frame.columnconfigure(0, weight=1)

    def _create_ui_tab(self, notebook: ttk.Notebook):
        """Create the UI settings tab."""
        frame = ttk.Frame(notebook, padding="20")
        notebook.add(frame, text="🎨 Interface")

        # Notification settings
        notif_group = ttk.LabelFrame(frame, text="Notifications", padding="15")
        notif_group.pack(fill=tk.X, pady=(0, 15))

        # Show transfer notifications
        self.notifications_var = tk.BooleanVar(value=self.settings.get("show_transfer_notifications", True))
        notif_check = ttk.Checkbutton(
            notif_group,
            text="Show transfer completion notifications",
            variable=self.notifications_var
        )
        notif_check.grid(row=0, column=0, sticky=tk.W, pady=(0, 5))

        # Auto clear completed transfers
        self.auto_clear_var = tk.BooleanVar(value=self.settings.get("auto_clear_completed_transfers", False))
        clear_check = ttk.Checkbutton(
            notif_group,
            text="Automatically clear completed transfers",
            variable=self.auto_clear_var
        )
        clear_check.grid(row=1, column=0, sticky=tk.W, pady=(0, 5))

        # Confirm deletions
        self.confirm_delete_var = tk.BooleanVar(value=self.settings.get("confirm_file_deletions", True))
        confirm_check = ttk.Checkbutton(
            notif_group,
            text="Confirm before deleting files",
            variable=self.confirm_delete_var
        )
        confirm_check.grid(row=2, column=0, sticky=tk.W, pady=(0, 5))

        # Window settings
        window_group = ttk.LabelFrame(frame, text="Window Settings", padding="15")
        window_group.pack(fill=tk.X)

        # Remember window size
        self.remember_size_var = tk.BooleanVar(value=self.settings.get("remember_window_size", True))
        size_check = ttk.Checkbutton(
            window_group,
            text="Remember window size and position",
            variable=self.remember_size_var
        )
        size_check.grid(row=0, column=0, sticky=tk.W)

        # Configure grid weights
        notif_group.columnconfigure(0, weight=1)
        window_group.columnconfigure(0, weight=1)

    def _create_advanced_tab(self, notebook: ttk.Notebook):
        """Create the advanced settings tab."""
        frame = ttk.Frame(notebook, padding="20")
        notebook.add(frame, text="⚙️ Advanced")

        # Logging settings
        log_group = ttk.LabelFrame(frame, text="Logging", padding="15")
        log_group.pack(fill=tk.X, pady=(0, 15))

        # Enable logging
        self.logging_var = tk.BooleanVar(value=self.settings.get("enable_logging", True))
        log_check = ttk.Checkbutton(
            log_group,
            text="Enable application logging",
            variable=self.logging_var
        )
        log_check.grid(row=0, column=0, sticky=tk.W, pady=(0, 10))

        # Log level
        level_frame = ttk.Frame(log_group)
        level_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        ttk.Label(level_frame, text="Log Level:").pack(side=tk.LEFT)
        self.log_level_var = tk.StringVar(value=self.settings.get("log_level", "INFO"))
        level_combo = ttk.Combobox(
            level_frame,
            textvariable=self.log_level_var,
            values=["DEBUG", "INFO", "WARNING", "ERROR"],
            state="readonly",
            width=10
        )
        level_combo.pack(side=tk.LEFT, padx=(10, 0))

        # Max log file size
        size_frame = ttk.Frame(log_group)
        size_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        ttk.Label(size_frame, text="Max log file size (MB):").pack(side=tk.LEFT)
        self.log_size_var = tk.IntVar(value=self.settings.get("max_log_file_size_mb", 10))
        size_spin = ttk.Spinbox(
            size_frame,
            from_=1,
            to=100,
            textvariable=self.log_size_var,
            width=10
        )
        size_spin.pack(side=tk.LEFT, padx=(10, 0))

        # Statistics and updates
        misc_group = ttk.LabelFrame(frame, text="Miscellaneous", padding="15")
        misc_group.pack(fill=tk.X)

        # Enable statistics
        self.stats_var = tk.BooleanVar(value=self.settings.get("enable_statistics", True))
        stats_check = ttk.Checkbutton(
            misc_group,
            text="Enable transfer statistics collection",
            variable=self.stats_var
        )
        stats_check.grid(row=0, column=0, sticky=tk.W, pady=(0, 5))

        # Auto update check
        self.update_var = tk.BooleanVar(value=self.settings.get("auto_update_check", True))
        update_check = ttk.Checkbutton(
            misc_group,
            text="Check for updates automatically",
            variable=self.update_var
        )
        update_check.grid(row=1, column=0, sticky=tk.W)

        # Configure grid weights
        log_group.columnconfigure(0, weight=1)
        misc_group.columnconfigure(0, weight=1)

    def _browse_download_dir(self):
        """Browse for download directory."""
        directory = filedialog.askdirectory(
            title="Select Download Directory",
            initialdir=self.download_dir_var.get()
        )
        if directory:
            self.download_dir_var.set(directory)

    def _create_network_tab(self, notebook: ttk.Notebook):
        """Create the network settings tab."""
        network_frame = ttk.Frame(notebook, padding="20")
        notebook.add(network_frame, text="🌐 Network")

        # Create scrollable frame for network settings
        canvas = tk.Canvas(network_frame)
        scrollbar = ttk.Scrollbar(network_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Speed Optimization Group
        speed_group = ttk.LabelFrame(scrollable_frame, text="Speed Optimization", padding="15")
        speed_group.pack(fill=tk.X, pady=(0, 15))

        # Enable speed optimization
        self.speed_optimization_var = tk.BooleanVar(value=self.settings.get("enable_speed_optimization", True))
        speed_check = ttk.Checkbutton(
            speed_group,
            text="Enable dynamic speed optimization",
            variable=self.speed_optimization_var,
            command=self._on_speed_optimization_changed
        )
        speed_check.grid(row=0, column=0, columnspan=3, sticky=tk.W, pady=(0, 10))

        # Speed mode
        ttk.Label(speed_group, text="Speed Mode:").grid(row=1, column=0, sticky=tk.W, pady=(0, 5))
        self.speed_mode_var = tk.StringVar(value=self.settings.get("speed_mode", "balanced"))
        speed_mode_combo = ttk.Combobox(speed_group, textvariable=self.speed_mode_var, width=15, state="readonly")
        speed_mode_combo['values'] = ['conservative', 'balanced', 'aggressive', 'custom']
        speed_mode_combo.grid(row=1, column=1, sticky=tk.W, pady=(0, 5))

        # Max speed limit
        ttk.Label(speed_group, text="Max Speed (Mbps):").grid(row=2, column=0, sticky=tk.W, pady=(0, 5))
        self.max_speed_var = tk.StringVar(value=str(self.settings.get("max_speed_mbps", 0)))
        max_speed_entry = ttk.Entry(speed_group, textvariable=self.max_speed_var, width=10)
        max_speed_entry.grid(row=2, column=1, sticky=tk.W, pady=(0, 5))
        ttk.Label(speed_group, text="(0 = unlimited)").grid(row=2, column=2, sticky=tk.W, padx=(10, 0), pady=(0, 5))

        # Congestion control
        self.congestion_control_var = tk.BooleanVar(value=self.settings.get("enable_congestion_control", True))
        congestion_check = ttk.Checkbutton(
            speed_group,
            text="Enable congestion control",
            variable=self.congestion_control_var
        )
        congestion_check.grid(row=3, column=0, columnspan=2, sticky=tk.W, pady=(5, 0))

        # Speed ramping
        self.speed_ramping_var = tk.BooleanVar(value=self.settings.get("enable_speed_ramping", True))
        ramping_check = ttk.Checkbutton(
            speed_group,
            text="Enable speed ramping",
            variable=self.speed_ramping_var
        )
        ramping_check.grid(row=4, column=0, columnspan=2, sticky=tk.W, pady=(5, 0))

        # Auto bandwidth detection
        self.auto_bandwidth_var = tk.BooleanVar(value=self.settings.get("auto_detect_bandwidth", True))
        auto_bandwidth_check = ttk.Checkbutton(
            speed_group,
            text="Auto-detect available bandwidth",
            variable=self.auto_bandwidth_var
        )
        auto_bandwidth_check.grid(row=5, column=0, columnspan=2, sticky=tk.W, pady=(5, 0))

        # Basic Network Settings Group
        basic_group = ttk.LabelFrame(scrollable_frame, text="Basic Network Settings", padding="15")
        basic_group.pack(fill=tk.X, pady=(0, 15))

        # Chunk size
        ttk.Label(basic_group, text="Chunk Size (KB):").grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        self.chunk_size_var = tk.StringVar(value=str(self.settings["chunk_size_kb"]))
        chunk_entry = ttk.Entry(basic_group, textvariable=self.chunk_size_var, width=10)
        chunk_entry.grid(row=0, column=1, sticky=tk.W, pady=(0, 5))
        ttk.Label(basic_group, text="(1-1024, recommended: 8-64)").grid(row=0, column=2, sticky=tk.W, padx=(10, 0), pady=(0, 5))

        # Connection timeout
        ttk.Label(basic_group, text="Connection Timeout (seconds):").grid(row=1, column=0, sticky=tk.W, pady=(0, 5))
        self.timeout_var = tk.StringVar(value=str(self.settings["connection_timeout"]))
        timeout_entry = ttk.Entry(basic_group, textvariable=self.timeout_var, width=10)
        timeout_entry.grid(row=1, column=1, sticky=tk.W, pady=(0, 5))

        # Max connections
        ttk.Label(basic_group, text="Max Concurrent Connections:").grid(row=2, column=0, sticky=tk.W, pady=(0, 5))
        self.max_conn_var = tk.StringVar(value=str(self.settings["max_connections"]))
        max_conn_entry = ttk.Entry(basic_group, textvariable=self.max_conn_var, width=10)
        max_conn_entry.grid(row=2, column=1, sticky=tk.W, pady=(0, 5))

        # Server Settings Group
        server_group = ttk.LabelFrame(scrollable_frame, text="Server Settings", padding="15")
        server_group.pack(fill=tk.X, pady=(0, 15))

        # Default server settings
        ttk.Label(server_group, text="Default Server Host:").grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        self.server_host_var = tk.StringVar(value=self.settings["default_server_host"])
        host_combo = ttk.Combobox(server_group, textvariable=self.server_host_var, width=20)
        host_combo['values'] = ['0.0.0.0', 'localhost'] + NetworkUtils.get_all_local_ips()
        host_combo.grid(row=0, column=1, columnspan=2, sticky=tk.W, pady=(0, 5))

        # Default server port
        ttk.Label(server_group, text="Default Server Port:").grid(row=1, column=0, sticky=tk.W, pady=(0, 5))
        self.server_port_var = tk.StringVar(value=str(self.settings["default_server_port"]))
        port_entry = ttk.Entry(server_group, textvariable=self.server_port_var, width=10)
        port_entry.grid(row=1, column=1, sticky=tk.W, pady=(0, 5))

        # Auto start server
        self.auto_start_var = tk.BooleanVar(value=self.settings["auto_start_server"])
        auto_start_check = ttk.Checkbutton(
            server_group,
            text="Auto-start server on application launch",
            variable=self.auto_start_var
        )
        auto_start_check.grid(row=2, column=0, columnspan=2, sticky=tk.W, pady=(5, 0))

        # Advanced Settings Group
        advanced_group = ttk.LabelFrame(scrollable_frame, text="Advanced Network Settings", padding="15")
        advanced_group.pack(fill=tk.X, pady=(0, 15))

        # Chunk size range
        ttk.Label(advanced_group, text="Chunk Size Range (KB):").grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        chunk_range_frame = ttk.Frame(advanced_group)
        chunk_range_frame.grid(row=0, column=1, columnspan=2, sticky=tk.W, pady=(0, 5))

        self.chunk_min_var = tk.StringVar(value=str(self.settings.get("chunk_size_min_kb", 8)))
        chunk_min_entry = ttk.Entry(chunk_range_frame, textvariable=self.chunk_min_var, width=8)
        chunk_min_entry.pack(side=tk.LEFT)

        ttk.Label(chunk_range_frame, text=" to ").pack(side=tk.LEFT, padx=(5, 5))

        self.chunk_max_var = tk.StringVar(value=str(self.settings.get("chunk_size_max_kb", 1024)))
        chunk_max_entry = ttk.Entry(chunk_range_frame, textvariable=self.chunk_max_var, width=8)
        chunk_max_entry.pack(side=tk.LEFT)

        # Buffer size range
        ttk.Label(advanced_group, text="Buffer Size Range (KB):").grid(row=1, column=0, sticky=tk.W, pady=(0, 5))
        buffer_range_frame = ttk.Frame(advanced_group)
        buffer_range_frame.grid(row=1, column=1, columnspan=2, sticky=tk.W, pady=(0, 5))

        self.buffer_min_var = tk.StringVar(value=str(self.settings.get("buffer_size_min_kb", 64)))
        buffer_min_entry = ttk.Entry(buffer_range_frame, textvariable=self.buffer_min_var, width=8)
        buffer_min_entry.pack(side=tk.LEFT)

        ttk.Label(buffer_range_frame, text=" to ").pack(side=tk.LEFT, padx=(5, 5))

        self.buffer_max_var = tk.StringVar(value=str(self.settings.get("buffer_size_max_kb", 4096)))
        buffer_max_entry = ttk.Entry(buffer_range_frame, textvariable=self.buffer_max_var, width=8)
        buffer_max_entry.pack(side=tk.LEFT)

    def _on_speed_optimization_changed(self):
        """Handle speed optimization toggle."""
        # This method can be used to enable/disable related controls
        pass
        
        ttk.Label(network_frame, text="Default Server Port:").grid(row=4, column=0, sticky=tk.W, pady=(0, 5))
        self.server_port_var = tk.StringVar(value=str(self.settings["default_server_port"]))
        port_entry = ttk.Entry(network_frame, textvariable=self.server_port_var, width=10)
        port_entry.grid(row=4, column=1, sticky=tk.W, pady=(0, 5))
    
    def _create_security_tab(self, notebook: ttk.Notebook):
        """Create the security settings tab."""
        security_frame = ttk.Frame(notebook, padding="10")
        notebook.add(security_frame, text="Security")
        
        # Enable encryption
        self.encryption_var = tk.BooleanVar(value=self.settings["enable_encryption"])
        encryption_check = ttk.Checkbutton(
            security_frame,
            text="Enable file encryption",
            variable=self.encryption_var
        )
        encryption_check.grid(row=0, column=0, sticky=tk.W, pady=(0, 10))
        
        # Encryption info
        encryption_info = ttk.Label(
            security_frame,
            text="When enabled, files are encrypted using AES-256 before transfer.\nThis provides security but may reduce transfer speed.",
            justify=tk.LEFT,
            style='Muted.TLabel'
        )
        encryption_info.grid(row=1, column=0, sticky=tk.W, pady=(0, 20))
        
        # Verify checksums
        self.checksum_var = tk.BooleanVar(value=self.settings["verify_checksums"])
        checksum_check = ttk.Checkbutton(
            security_frame,
            text="Verify file checksums",
            variable=self.checksum_var
        )
        checksum_check.grid(row=2, column=0, sticky=tk.W, pady=(0, 10))
        
        # Checksum info
        checksum_info = ttk.Label(
            security_frame,
            text="When enabled, file integrity is verified using MD5 checksums.\nThis ensures files are transferred without corruption.",
            justify=tk.LEFT,
            style='Muted.TLabel'
        )
        checksum_info.grid(row=3, column=0, sticky=tk.W, pady=(0, 20))

        # Enable compression
        self.compression_var = tk.BooleanVar(value=self.settings.get("enable_compression", True))
        compression_check = ttk.Checkbutton(
            security_frame,
            text="Enable file compression",
            variable=self.compression_var
        )
        compression_check.grid(row=4, column=0, sticky=tk.W, pady=(0, 10))

        # Compression info
        compression_info = ttk.Label(
            security_frame,
            text="When enabled, files are compressed before transfer to reduce size.\nThis can significantly speed up transfers for text files.",
            justify=tk.LEFT,
            style='Muted.TLabel'
        )
        compression_info.grid(row=5, column=0, sticky=tk.W)
    
    def _create_general_tab(self, notebook: ttk.Notebook):
        """Create the general settings tab."""
        general_frame = ttk.Frame(notebook, padding="10")
        notebook.add(general_frame, text="General")
        
        # Default download directory
        ttk.Label(general_frame, text="Default Download Directory:").grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        
        dir_frame = ttk.Frame(general_frame)
        dir_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        dir_frame.columnconfigure(0, weight=1)
        
        self.download_dir_var = tk.StringVar(value=self.settings["default_download_dir"])
        dir_entry = ttk.Entry(dir_frame, textvariable=self.download_dir_var)
        dir_entry.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 5))
        
        ttk.Button(dir_frame, text="Browse", command=self._browse_download_dir).grid(row=0, column=1)
        
        # Auto-start server
        self.auto_start_var = tk.BooleanVar(value=self.settings["auto_start_server"])
        auto_start_check = ttk.Checkbutton(
            general_frame,
            text="Auto-start server on application launch",
            variable=self.auto_start_var
        )
        auto_start_check.grid(row=2, column=0, sticky=tk.W, pady=(10, 0))
        
        # Configure grid weights
        general_frame.columnconfigure(0, weight=1)
    
    def _center_dialog(self):
        """Center the dialog on the parent window."""
        self.dialog.update_idletasks()
        
        # Get parent window position and size
        parent_x = self.parent.winfo_x()
        parent_y = self.parent.winfo_y()
        parent_width = self.parent.winfo_width()
        parent_height = self.parent.winfo_height()
        
        # Get dialog size
        dialog_width = self.dialog.winfo_width()
        dialog_height = self.dialog.winfo_height()
        
        # Calculate center position
        x = parent_x + (parent_width - dialog_width) // 2
        y = parent_y + (parent_height - dialog_height) // 2
        
        self.dialog.geometry(f"{dialog_width}x{dialog_height}+{x}+{y}")
    
    def _browse_download_dir(self):
        """Browse for download directory."""
        directory = filedialog.askdirectory(
            title="Select Default Download Directory",
            initialdir=self.download_dir_var.get()
        )
        
        if directory:
            self.download_dir_var.set(directory)
    
    def _validate_settings(self) -> bool:
        """Validate the current settings."""
        try:
            # Validate chunk size
            chunk_size = int(self.chunk_size_var.get())
            if not (1 <= chunk_size <= 1024):
                messagebox.showerror("Invalid Setting", "Chunk size must be between 1 and 1024 KB")
                return False
            
            # Validate timeout
            timeout = int(self.timeout_var.get())
            if not (1 <= timeout <= 300):
                messagebox.showerror("Invalid Setting", "Connection timeout must be between 1 and 300 seconds")
                return False
            
            # Validate max connections
            max_conn = int(self.max_conn_var.get())
            if not (1 <= max_conn <= 100):
                messagebox.showerror("Invalid Setting", "Max connections must be between 1 and 100")
                return False
            
            # Validate server port
            server_port = int(self.server_port_var.get())
            if not NetworkUtils.is_valid_port(server_port):
                messagebox.showerror("Invalid Setting", "Server port must be between 1 and 65535")
                return False
            
            # Validate download directory
            download_dir = self.download_dir_var.get().strip()
            if not download_dir:
                messagebox.showerror("Invalid Setting", "Download directory cannot be empty")
                return False
            
            return True
            
        except ValueError:
            messagebox.showerror("Invalid Setting", "Please enter valid numeric values")
            return False
    
    def _reset_to_defaults(self):
        """Reset all settings to default values."""
        if messagebox.askyesno("Reset Settings",
                              "Are you sure you want to reset all settings to their default values?"):
            default_settings = self._get_default_settings()
            self._load_settings(default_settings)

    def _load_settings(self, settings: Dict[str, Any]):
        """Load settings into the dialog controls."""
        # Compression settings
        if hasattr(self, 'compression_var'):
            self.compression_var.set(settings.get("enable_compression", True))
        if hasattr(self, 'compression_method_var'):
            self.compression_method_var.set(settings.get("compression_method", "auto"))
        if hasattr(self, 'compression_level_var'):
            self.compression_level_var.set(settings.get("compression_level", 6))
        if hasattr(self, 'min_compression_size_var'):
            self.min_compression_size_var.set(settings.get("min_compression_size_kb", 1))
        if hasattr(self, 'skip_compressed_var'):
            self.skip_compressed_var.set(settings.get("skip_compressed_files", True))

        # File transfer settings
        if hasattr(self, 'overwrite_var'):
            self.overwrite_var.set(settings.get("overwrite_existing_files", False))
        if hasattr(self, 'subdirs_var'):
            self.subdirs_var.set(settings.get("create_subdirectories", True))
        if hasattr(self, 'timestamps_var'):
            self.timestamps_var.set(settings.get("preserve_file_timestamps", True))
        if hasattr(self, 'download_dir_var'):
            self.download_dir_var.set(settings.get("default_download_dir", ""))

        # UI settings
        if hasattr(self, 'notifications_var'):
            self.notifications_var.set(settings.get("show_transfer_notifications", True))
        if hasattr(self, 'auto_clear_var'):
            self.auto_clear_var.set(settings.get("auto_clear_completed_transfers", False))
        if hasattr(self, 'confirm_delete_var'):
            self.confirm_delete_var.set(settings.get("confirm_file_deletions", True))
        if hasattr(self, 'remember_size_var'):
            self.remember_size_var.set(settings.get("remember_window_size", True))

        # Advanced settings
        if hasattr(self, 'logging_var'):
            self.logging_var.set(settings.get("enable_logging", True))
        if hasattr(self, 'log_level_var'):
            self.log_level_var.set(settings.get("log_level", "INFO"))
        if hasattr(self, 'log_size_var'):
            self.log_size_var.set(settings.get("max_log_file_size_mb", 10))
        if hasattr(self, 'stats_var'):
            self.stats_var.set(settings.get("enable_statistics", True))
        if hasattr(self, 'update_var'):
            self.update_var.set(settings.get("auto_update_check", True))

        # Speed optimization settings
        if hasattr(self, 'speed_optimization_var'):
            self.speed_optimization_var.set(settings.get("enable_speed_optimization", True))
        if hasattr(self, 'speed_mode_var'):
            self.speed_mode_var.set(settings.get("speed_mode", "balanced"))
        if hasattr(self, 'max_speed_var'):
            self.max_speed_var.set(settings.get("max_speed_mbps", 0))
        if hasattr(self, 'congestion_control_var'):
            self.congestion_control_var.set(settings.get("enable_congestion_control", True))
        if hasattr(self, 'speed_ramping_var'):
            self.speed_ramping_var.set(settings.get("enable_speed_ramping", True))
        if hasattr(self, 'auto_bandwidth_var'):
            self.auto_bandwidth_var.set(settings.get("auto_detect_bandwidth", True))
        if hasattr(self, 'chunk_min_var'):
            self.chunk_min_var.set(settings.get("chunk_size_min_kb", 8))
        if hasattr(self, 'chunk_max_var'):
            self.chunk_max_var.set(settings.get("chunk_size_max_kb", 1024))
        if hasattr(self, 'buffer_min_var'):
            self.buffer_min_var.set(settings.get("buffer_size_min_kb", 64))
        if hasattr(self, 'buffer_max_var'):
            self.buffer_max_var.set(settings.get("buffer_size_max_kb", 4096))

        # Network and security settings (existing)
        if hasattr(self, 'chunk_size_var'):
            self.chunk_size_var.set(settings.get("chunk_size_kb", 8))
        if hasattr(self, 'timeout_var'):
            self.timeout_var.set(settings.get("connection_timeout", 10))
        if hasattr(self, 'max_conn_var'):
            self.max_conn_var.set(settings.get("max_connections", 5))
        if hasattr(self, 'server_port_var'):
            self.server_port_var.set(settings.get("default_server_port", 8888))
        if hasattr(self, 'server_host_var'):
            self.server_host_var.set(settings.get("default_server_host", "0.0.0.0"))
        if hasattr(self, 'auto_start_var'):
            self.auto_start_var.set(settings.get("auto_start_server", False))
        if hasattr(self, 'encryption_var'):
            self.encryption_var.set(settings.get("enable_encryption", False))
        if hasattr(self, 'checksum_var'):
            self.checksum_var.set(settings.get("verify_checksums", True))

    def _get_current_settings(self) -> Dict[str, Any]:
        """Get the current settings from the dialog."""
        settings = {}

        # Compression settings
        if hasattr(self, 'compression_var'):
            settings["enable_compression"] = self.compression_var.get()
        if hasattr(self, 'compression_method_var'):
            settings["compression_method"] = self.compression_method_var.get()
        if hasattr(self, 'compression_level_var'):
            settings["compression_level"] = self.compression_level_var.get()
        if hasattr(self, 'min_compression_size_var'):
            settings["min_compression_size_kb"] = self.min_compression_size_var.get()
        if hasattr(self, 'skip_compressed_var'):
            settings["skip_compressed_files"] = self.skip_compressed_var.get()

        # File transfer settings
        if hasattr(self, 'overwrite_var'):
            settings["overwrite_existing_files"] = self.overwrite_var.get()
        if hasattr(self, 'subdirs_var'):
            settings["create_subdirectories"] = self.subdirs_var.get()
        if hasattr(self, 'timestamps_var'):
            settings["preserve_file_timestamps"] = self.timestamps_var.get()
        if hasattr(self, 'download_dir_var'):
            settings["default_download_dir"] = self.download_dir_var.get()

        # UI settings
        if hasattr(self, 'notifications_var'):
            settings["show_transfer_notifications"] = self.notifications_var.get()
        if hasattr(self, 'auto_clear_var'):
            settings["auto_clear_completed_transfers"] = self.auto_clear_var.get()
        if hasattr(self, 'confirm_delete_var'):
            settings["confirm_file_deletions"] = self.confirm_delete_var.get()
        if hasattr(self, 'remember_size_var'):
            settings["remember_window_size"] = self.remember_size_var.get()

        # Advanced settings
        if hasattr(self, 'logging_var'):
            settings["enable_logging"] = self.logging_var.get()
        if hasattr(self, 'log_level_var'):
            settings["log_level"] = self.log_level_var.get()
        if hasattr(self, 'log_size_var'):
            settings["max_log_file_size_mb"] = self.log_size_var.get()
        if hasattr(self, 'stats_var'):
            settings["enable_statistics"] = self.stats_var.get()
        if hasattr(self, 'update_var'):
            settings["auto_update_check"] = self.update_var.get()

        # Speed optimization settings
        if hasattr(self, 'speed_optimization_var'):
            settings["enable_speed_optimization"] = self.speed_optimization_var.get()
        if hasattr(self, 'speed_mode_var'):
            settings["speed_mode"] = self.speed_mode_var.get()
        if hasattr(self, 'max_speed_var'):
            settings["max_speed_mbps"] = float(self.max_speed_var.get())
        if hasattr(self, 'congestion_control_var'):
            settings["enable_congestion_control"] = self.congestion_control_var.get()
        if hasattr(self, 'speed_ramping_var'):
            settings["enable_speed_ramping"] = self.speed_ramping_var.get()
        if hasattr(self, 'auto_bandwidth_var'):
            settings["auto_detect_bandwidth"] = self.auto_bandwidth_var.get()
        if hasattr(self, 'chunk_min_var'):
            settings["chunk_size_min_kb"] = int(self.chunk_min_var.get())
        if hasattr(self, 'chunk_max_var'):
            settings["chunk_size_max_kb"] = int(self.chunk_max_var.get())
        if hasattr(self, 'buffer_min_var'):
            settings["buffer_size_min_kb"] = int(self.buffer_min_var.get())
        if hasattr(self, 'buffer_max_var'):
            settings["buffer_size_max_kb"] = int(self.buffer_max_var.get())

        # Network and security settings (existing)
        if hasattr(self, 'chunk_size_var'):
            settings["chunk_size_kb"] = int(self.chunk_size_var.get())
        if hasattr(self, 'timeout_var'):
            settings["connection_timeout"] = int(self.timeout_var.get())
        if hasattr(self, 'max_conn_var'):
            settings["max_connections"] = int(self.max_conn_var.get())
        if hasattr(self, 'encryption_var'):
            settings["enable_encryption"] = self.encryption_var.get()
        if hasattr(self, 'checksum_var'):
            settings["verify_checksums"] = self.checksum_var.get()
        if hasattr(self, 'auto_start_var'):
            settings["auto_start_server"] = self.auto_start_var.get()
        if hasattr(self, 'server_port_var'):
            settings["default_server_port"] = int(self.server_port_var.get())
        if hasattr(self, 'server_host_var'):
            settings["default_server_host"] = self.server_host_var.get()

        return settings

    def save_settings_to_file(self, settings: Dict[str, Any]):
        """Save settings to file for persistence."""
        try:
            settings_dir = Path.home() / ".filetransfer"
            settings_dir.mkdir(exist_ok=True)

            settings_file = settings_dir / "settings.json"
            with open(settings_file, 'w') as f:
                json.dump(settings, f, indent=2)

            self.logger.info(f"Settings saved to {settings_file}")
        except Exception as e:
            self.logger.error(f"Failed to save settings: {e}")

    def load_settings_from_file(self) -> Dict[str, Any]:
        """Load settings from file."""
        try:
            settings_file = Path.home() / ".filetransfer" / "settings.json"
            if settings_file.exists():
                with open(settings_file, 'r') as f:
                    return json.load(f)
        except Exception as e:
            self.logger.error(f"Failed to load settings: {e}")

        return self._get_default_settings()
    
    def _on_ok(self):
        """Handle OK button click."""
        if self._validate_settings():
            self.result = self._get_current_settings()
            self.dialog.destroy()
    
    def _on_cancel(self):
        """Handle Cancel button click."""
        self.result = None
        self.dialog.destroy()
    
    def _on_apply(self):
        """Handle Apply button click."""
        if self._validate_settings():
            self.result = self._get_current_settings()

            # Save settings to file for persistence
            self.save_settings_to_file(self.result)

            # Don't close dialog, just return the settings
            messagebox.showinfo("Settings Applied", "Settings have been applied successfully!")
    
    def _on_reset(self):
        """Reset settings to defaults."""
        defaults = self._get_default_settings()

        self.chunk_size_var.set(str(defaults["chunk_size_kb"]))
        self.timeout_var.set(str(defaults["connection_timeout"]))
        self.max_conn_var.set(str(defaults["max_connections"]))
        self.encryption_var.set(defaults["enable_encryption"])
        self.checksum_var.set(defaults["verify_checksums"])
        self.compression_var.set(defaults["enable_compression"])
        self.download_dir_var.set(defaults["default_download_dir"])
        self.auto_start_var.set(defaults["auto_start_server"])
        self.server_port_var.set(str(defaults["default_server_port"]))
        self.server_host_var.set(defaults["default_server_host"])
    
    def show(self) -> Optional[Dict[str, Any]]:
        """
        Show the dialog and return the settings.
        
        Returns:
            Settings dictionary or None if cancelled
        """
        self.dialog.wait_window()
        return self.result
